<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>撇号用法</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            transition: none;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 撇号基本概念 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号的基本概念</h2>
            <p class="text-gray-700 mb-4">撇号（apostrophe）是英语中最重要的标点符号之一，主要用于表示所有格和缩写。正确使用撇号对于准确表达意思至关重要。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">撇号符号</div>
                    <div class="keyword text-lg mb-1">'</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈpɒstrəfi/</div>
                    <div class="text-gray-700">撇号标点符号</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基本用途</div>
                    <div class="keyword text-lg mb-1">possession</div>
                    <div class="text-sm text-gray-600 mb-1">/pəˈzeʃən/</div>
                    <div class="text-gray-700">表示所有格</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写用途</div>
                    <div class="keyword text-lg mb-1">contraction</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈtrækʃən/</div>
                    <div class="text-gray-700">表示缩写形式</div>
                </div>
            </div>
        </section>

        <!-- 所有格用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">所有格用法</h2>
            <p class="text-gray-700 mb-4">撇号最主要的用途是表示所有格，即表示某物属于某人或某事物。根据名词的不同形式，撇号的位置也会发生变化。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">单数名词所有格</h3>
            <p class="text-gray-700 mb-4">对于单数名词，在名词后面加上撇号和s（'s）来表示所有格。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">人名所有格</div>
                    <div class="keyword text-lg mb-1">John's car</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɒnz kɑːr/</div>
                    <div class="text-gray-700">约翰的汽车</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">物品所有格</div>
                    <div class="keyword text-lg mb-1">the cat's tail</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæts teɪl/</div>
                    <div class="text-gray-700">猫的尾巴</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点所有格</div>
                    <div class="keyword text-lg mb-1">the city's center</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈsɪtiz ˈsentər/</div>
                    <div class="text-gray-700">城市的中心</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间所有格</div>
                    <div class="keyword text-lg mb-1">today's news</div>
                    <div class="text-sm text-gray-600 mb-1">/təˈdeɪz njuːz/</div>
                    <div class="text-gray-700">今天的新闻</div>
                </div>
                
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">公司所有格</div>
                    <div class="keyword text-lg mb-1">the company's policy</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəniz ˈpɒləsi/</div>
                    <div class="text-gray-700">公司的政策</div>
                </div>
                
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学校所有格</div>
                    <div class="keyword text-lg mb-1">the school's library</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə skuːlz ˈlaɪbrəri/</div>
                    <div class="text-gray-700">学校的图书馆</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">复数名词所有格</h3>
            <p class="text-gray-700 mb-4">对于以s结尾的复数名词，只需在末尾加上撇号（'）。对于不以s结尾的复数名词，则加上撇号和s（'s）。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">规则复数</div>
                    <div class="keyword text-lg mb-1">the students' books</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstjuːdənts bʊks/</div>
                    <div class="text-gray-700">学生们的书</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不规则复数</div>
                    <div class="keyword text-lg mb-1">the children's toys</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrənz tɔɪz/</div>
                    <div class="text-gray-700">孩子们的玩具</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动物复数</div>
                    <div class="keyword text-lg mb-1">the dogs' owner</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə dɒɡz ˈəʊnər/</div>
                    <div class="text-gray-700">狗狗们的主人</div>
                </div>
                
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">职业复数</div>
                    <div class="keyword text-lg mb-1">the teachers' meeting</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiːtʃərz ˈmiːtɪŋ/</div>
                    <div class="text-gray-700">老师们的会议</div>
                </div>
            </div>
        </section>

        <!-- 以s结尾的单数名词 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">以s结尾的单数名词所有格</h2>
            <p class="text-gray-700 mb-4">对于以s结尾的单数名词，有两种处理方式：可以只加撇号（'），也可以加撇号和s（'s）。现代英语更倾向于使用后者。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">人名结尾s</div>
                    <div class="keyword text-lg mb-1">James's book</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdʒeɪmzɪz bʊk/</div>
                    <div class="text-gray-700">詹姆斯的书</div>
                </div>
                
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">古典名字</div>
                    <div class="keyword text-lg mb-1">Jesus' teachings</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdʒiːzəs ˈtiːtʃɪŋz/</div>
                    <div class="text-gray-700">耶稣的教导</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">普通名词</div>
                    <div class="keyword text-lg mb-1">the boss's office</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈbɒsɪz ˈɒfɪs/</div>
                    <div class="text-gray-700">老板的办公室</div>
                </div>
                
                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地名结尾s</div>
                    <div class="keyword text-lg mb-1">Texas's capital</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈteksəsɪz ˈkæpɪtəl/</div>
                    <div class="text-gray-700">德克萨斯州的首府</div>
                </div>
            </div>
        </section>

        <!-- 缩写用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">缩写用法</h2>
            <p class="text-gray-700 mb-4">撇号的另一个重要用途是表示缩写，即将两个词合并成一个词，省略的字母用撇号代替。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">be动词缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">I am缩写</div>
                    <div class="keyword text-lg mb-1">I'm</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm/</div>
                    <div class="text-gray-700">我是</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">you are缩写</div>
                    <div class="keyword text-lg mb-1">you're</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊər/</div>
                    <div class="text-gray-700">你是</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">he is缩写</div>
                    <div class="keyword text-lg mb-1">he's</div>
                    <div class="text-sm text-gray-600 mb-1">/hiːz/</div>
                    <div class="text-gray-700">他是</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">she is缩写</div>
                    <div class="keyword text-lg mb-1">she's</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃiːz/</div>
                    <div class="text-gray-700">她是</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">it is缩写</div>
                    <div class="keyword text-lg mb-1">it's</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts/</div>
                    <div class="text-gray-700">它是</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">we are缩写</div>
                    <div class="keyword text-lg mb-1">we're</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪər/</div>
                    <div class="text-gray-700">我们是</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">they are缩写</div>
                    <div class="keyword text-lg mb-1">they're</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeər/</div>
                    <div class="text-gray-700">他们是</div>
                </div>
                
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">that is缩写</div>
                    <div class="keyword text-lg mb-1">that's</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts/</div>
                    <div class="text-gray-700">那是</div>
                </div>
                
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">there is缩写</div>
                    <div class="keyword text-lg mb-1">there's</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeərz/</div>
                    <div class="text-gray-700">有</div>
                </div>
            </div>
        </section>

        <!-- have动词缩写 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-3 text-gray-800">have动词缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">I have缩写</div>
                    <div class="keyword text-lg mb-1">I've</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪv/</div>
                    <div class="text-gray-700">我有/我已经</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">you have缩写</div>
                    <div class="keyword text-lg mb-1">you've</div>
                    <div class="text-sm text-gray-600 mb-1">/juːv/</div>
                    <div class="text-gray-700">你有/你已经</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">we have缩写</div>
                    <div class="keyword text-lg mb-1">we've</div>
                    <div class="text-sm text-gray-600 mb-1">/wiːv/</div>
                    <div class="text-gray-700">我们有/我们已经</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">they have缩写</div>
                    <div class="keyword text-lg mb-1">they've</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪv/</div>
                    <div class="text-gray-700">他们有/他们已经</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">he has缩写</div>
                    <div class="keyword text-lg mb-1">he's</div>
                    <div class="text-sm text-gray-600 mb-1">/hiːz/</div>
                    <div class="text-gray-700">他有/他已经</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">she has缩写</div>
                    <div class="keyword text-lg mb-1">she's</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃiːz/</div>
                    <div class="text-gray-700">她有/她已经</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">will动词缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">I will缩写</div>
                    <div class="keyword text-lg mb-1">I'll</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪl/</div>
                    <div class="text-gray-700">我将要</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">you will缩写</div>
                    <div class="keyword text-lg mb-1">you'll</div>
                    <div class="text-sm text-gray-600 mb-1">/juːl/</div>
                    <div class="text-gray-700">你将要</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">he will缩写</div>
                    <div class="keyword text-lg mb-1">he'll</div>
                    <div class="text-sm text-gray-600 mb-1">/hiːl/</div>
                    <div class="text-gray-700">他将要</div>
                </div>

                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">she will缩写</div>
                    <div class="keyword text-lg mb-1">she'll</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃiːl/</div>
                    <div class="text-gray-700">她将要</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">it will缩写</div>
                    <div class="keyword text-lg mb-1">it'll</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪtl/</div>
                    <div class="text-gray-700">它将要</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">we will缩写</div>
                    <div class="keyword text-lg mb-1">we'll</div>
                    <div class="text-sm text-gray-600 mb-1">/wiːl/</div>
                    <div class="text-gray-700">我们将要</div>
                </div>

                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">they will缩写</div>
                    <div class="keyword text-lg mb-1">they'll</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪl/</div>
                    <div class="text-gray-700">他们将要</div>
                </div>

                <div class="card bg-blue-100 border border-blue-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">that will缩写</div>
                    <div class="keyword text-lg mb-1">that'll</div>
                    <div class="text-sm text-gray-600 mb-1">/ðætl/</div>
                    <div class="text-gray-700">那将要</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">there will缩写</div>
                    <div class="keyword text-lg mb-1">there'll</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeərl/</div>
                    <div class="text-gray-700">将会有</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">would动词缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-100 border border-yellow-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">I would缩写</div>
                    <div class="keyword text-lg mb-1">I'd</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪd/</div>
                    <div class="text-gray-700">我会/我想要</div>
                </div>

                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">you would缩写</div>
                    <div class="keyword text-lg mb-1">you'd</div>
                    <div class="text-sm text-gray-600 mb-1">/juːd/</div>
                    <div class="text-gray-700">你会/你想要</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">he would缩写</div>
                    <div class="keyword text-lg mb-1">he'd</div>
                    <div class="text-sm text-gray-600 mb-1">/hiːd/</div>
                    <div class="text-gray-700">他会/他想要</div>
                </div>

                <div class="card bg-indigo-100 border border-indigo-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">she would缩写</div>
                    <div class="keyword text-lg mb-1">she'd</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃiːd/</div>
                    <div class="text-gray-700">她会/她想要</div>
                </div>

                <div class="card bg-teal-100 border border-teal-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">it would缩写</div>
                    <div class="keyword text-lg mb-1">it'd</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪtd/</div>
                    <div class="text-gray-700">它会</div>
                </div>

                <div class="card bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">we would缩写</div>
                    <div class="keyword text-lg mb-1">we'd</div>
                    <div class="text-sm text-gray-600 mb-1">/wiːd/</div>
                    <div class="text-gray-700">我们会/我们想要</div>
                </div>

                <div class="card bg-cyan-100 border border-cyan-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">they would缩写</div>
                    <div class="keyword text-lg mb-1">they'd</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪd/</div>
                    <div class="text-gray-700">他们会/他们想要</div>
                </div>

                <div class="card bg-lime-100 border border-lime-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">that would缩写</div>
                    <div class="keyword text-lg mb-1">that'd</div>
                    <div class="text-sm text-gray-600 mb-1">/ðætd/</div>
                    <div class="text-gray-700">那会</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">there would缩写</div>
                    <div class="keyword text-lg mb-1">there'd</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeərd/</div>
                    <div class="text-gray-700">会有</div>
                </div>
            </div>
        </section>

        <!-- 否定缩写 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">否定缩写</h2>
            <p class="text-gray-700 mb-4">撇号也用于表示否定缩写，将not与前面的动词合并，省略字母o，用撇号代替。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">be动词否定缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">is not缩写</div>
                    <div class="keyword text-lg mb-1">isn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɪznt/</div>
                    <div class="text-gray-700">不是</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">are not缩写</div>
                    <div class="keyword text-lg mb-1">aren't</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːrnt/</div>
                    <div class="text-gray-700">不是</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">was not缩写</div>
                    <div class="keyword text-lg mb-1">wasn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwʌznt/</div>
                    <div class="text-gray-700">不是（过去式）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">were not缩写</div>
                    <div class="keyword text-lg mb-1">weren't</div>
                    <div class="text-sm text-gray-600 mb-1">/wɜːrnt/</div>
                    <div class="text-gray-700">不是（过去式复数）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">助动词否定缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">do not缩写</div>
                    <div class="keyword text-lg mb-1">don't</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt/</div>
                    <div class="text-gray-700">不做</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">does not缩写</div>
                    <div class="keyword text-lg mb-1">doesn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdʌznt/</div>
                    <div class="text-gray-700">不做（第三人称单数）</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">did not缩写</div>
                    <div class="keyword text-lg mb-1">didn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɪdnt/</div>
                    <div class="text-gray-700">没有做（过去式）</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">have not缩写</div>
                    <div class="keyword text-lg mb-1">haven't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhævnt/</div>
                    <div class="text-gray-700">没有</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">has not缩写</div>
                    <div class="keyword text-lg mb-1">hasn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhæznt/</div>
                    <div class="text-gray-700">没有（第三人称单数）</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">had not缩写</div>
                    <div class="keyword text-lg mb-1">hadn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhædnt/</div>
                    <div class="text-gray-700">没有（过去完成时）</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">will not缩写</div>
                    <div class="keyword text-lg mb-1">won't</div>
                    <div class="text-sm text-gray-600 mb-1">/woʊnt/</div>
                    <div class="text-gray-700">将不会</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">would not缩写</div>
                    <div class="keyword text-lg mb-1">wouldn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwʊdnt/</div>
                    <div class="text-gray-700">不会</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">should not缩写</div>
                    <div class="keyword text-lg mb-1">shouldn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈʃʊdnt/</div>
                    <div class="text-gray-700">不应该</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">情态动词否定缩写</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">can not缩写</div>
                    <div class="keyword text-lg mb-1">can't</div>
                    <div class="text-sm text-gray-600 mb-1">/kænt/</div>
                    <div class="text-gray-700">不能</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">could not缩写</div>
                    <div class="keyword text-lg mb-1">couldn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkʊdnt/</div>
                    <div class="text-gray-700">不能（过去式）</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">must not缩写</div>
                    <div class="keyword text-lg mb-1">mustn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmʌsnt/</div>
                    <div class="text-gray-700">不能/禁止</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">might not缩写</div>
                    <div class="keyword text-lg mb-1">mightn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmaɪtnt/</div>
                    <div class="text-gray-700">可能不</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">need not缩写</div>
                    <div class="keyword text-lg mb-1">needn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈniːdnt/</div>
                    <div class="text-gray-700">不需要</div>
                </div>

                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">ought not缩写</div>
                    <div class="keyword text-lg mb-1">oughtn't</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɔːtnt/</div>
                    <div class="text-gray-700">不应该</div>
                </div>
            </div>
        </section>

        <!-- 特殊缩写形式 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">特殊缩写形式</h2>
            <p class="text-gray-700 mb-4">除了常见的动词缩写外，还有一些特殊的缩写形式，这些在日常对话和非正式写作中经常出现。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">let us缩写</div>
                    <div class="keyword text-lg mb-1">let's</div>
                    <div class="text-sm text-gray-600 mb-1">/lets/</div>
                    <div class="text-gray-700">让我们</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">of the clock缩写</div>
                    <div class="keyword text-lg mb-1">o'clock</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈklɒk/</div>
                    <div class="text-gray-700">点钟</div>
                </div>

                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">madam缩写</div>
                    <div class="keyword text-lg mb-1">ma'am</div>
                    <div class="text-sm text-gray-600 mb-1">/mæm/</div>
                    <div class="text-gray-700">女士</div>
                </div>

                <div class="card bg-blue-100 border border-blue-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">rock and roll缩写</div>
                    <div class="keyword text-lg mb-1">rock 'n' roll</div>
                    <div class="text-sm text-gray-600 mb-1">/rɒk ən roʊl/</div>
                    <div class="text-gray-700">摇滚乐</div>
                </div>
            </div>
        </section>

        <!-- 复合所有格 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">复合所有格</h2>
            <p class="text-gray-700 mb-4">当多个名词共同拥有某物时，只在最后一个名词后加撇号。当每个名词分别拥有时，每个名词都要加撇号。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">共同所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">共同拥有</div>
                    <div class="keyword text-lg mb-1">Tom and Jerry's house</div>
                    <div class="text-sm text-gray-600 mb-1">/tɒm ænd ˈdʒeri haʊs/</div>
                    <div class="text-gray-700">汤姆和杰瑞的房子（共同拥有）</div>
                </div>

                <div class="card bg-yellow-100 border border-yellow-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">公司合作</div>
                    <div class="keyword text-lg mb-1">Smith and Johnson's project</div>
                    <div class="text-sm text-gray-600 mb-1">/smɪθ ænd ˈdʒɒnsənz ˈprɒdʒekt/</div>
                    <div class="text-gray-700">史密斯和约翰逊的项目（共同项目）</div>
                </div>

                <div class="card bg-purple-100 border border-purple-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">夫妻财产</div>
                    <div class="keyword text-lg mb-1">John and Mary's car</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɒn ænd ˈmeəriz kɑːr/</div>
                    <div class="text-gray-700">约翰和玛丽的汽车（共同拥有）</div>
                </div>

                <div class="card bg-pink-100 border border-pink-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">兄弟姐妹</div>
                    <div class="keyword text-lg mb-1">the brothers and sisters' room</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈbrʌðərz ænd ˈsɪstərz ruːm/</div>
                    <div class="text-gray-700">兄弟姐妹们的房间（共用）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">分别所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-100 border border-indigo-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">分别拥有</div>
                    <div class="keyword text-lg mb-1">Tom's and Jerry's books</div>
                    <div class="text-sm text-gray-600 mb-1">/tɒmz ænd ˈdʒeriz bʊks/</div>
                    <div class="text-gray-700">汤姆的书和杰瑞的书（分别拥有）</div>
                </div>

                <div class="card bg-teal-100 border border-teal-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">个人物品</div>
                    <div class="keyword text-lg mb-1">John's and Mary's phones</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɒnz ænd ˈmeəriz foʊnz/</div>
                    <div class="text-gray-700">约翰的手机和玛丽的手机</div>
                </div>

                <div class="card bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学生作业</div>
                    <div class="keyword text-lg mb-1">Lisa's and Mike's homework</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈliːsəz ænd maɪks ˈhoʊmwɜːrk/</div>
                    <div class="text-gray-700">丽莎的作业和迈克的作业</div>
                </div>

                <div class="card bg-cyan-100 border border-cyan-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">教师课程</div>
                    <div class="keyword text-lg mb-1">Mr. Smith's and Ms. Brown's classes</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɪstər smɪθs ænd mɪz braʊnz ˈklæsɪz/</div>
                    <div class="text-gray-700">史密斯先生的课和布朗女士的课</div>
                </div>
            </div>
        </section>

        <!-- 时间和距离的所有格 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">时间和距离的所有格</h2>
            <p class="text-gray-700 mb-4">表示时间长度、距离或价值时，也需要使用撇号来表示所有格关系。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">时间所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-lime-100 border border-lime-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一天时间</div>
                    <div class="keyword text-lg mb-1">a day's work</div>
                    <div class="text-sm text-gray-600 mb-1">/ə deɪz wɜːrk/</div>
                    <div class="text-gray-700">一天的工作</div>
                </div>

                <div class="card bg-emerald-100 border border-emerald-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一周时间</div>
                    <div class="keyword text-lg mb-1">a week's vacation</div>
                    <div class="text-sm text-gray-600 mb-1">/ə wiːks veɪˈkeɪʃən/</div>
                    <div class="text-gray-700">一周的假期</div>
                </div>

                <div class="card bg-violet-100 border border-violet-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一个月时间</div>
                    <div class="keyword text-lg mb-1">a month's salary</div>
                    <div class="text-sm text-gray-600 mb-1">/ə mʌnθs ˈsæləri/</div>
                    <div class="text-gray-700">一个月的薪水</div>
                </div>

                <div class="card bg-rose-100 border border-rose-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一年时间</div>
                    <div class="keyword text-lg mb-1">a year's experience</div>
                    <div class="text-sm text-gray-600 mb-1">/ə jɪrz ɪkˈspɪriəns/</div>
                    <div class="text-gray-700">一年的经验</div>
                </div>

                <div class="card bg-sky-100 border border-sky-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">两天时间</div>
                    <div class="keyword text-lg mb-1">two days' notice</div>
                    <div class="text-sm text-gray-600 mb-1">/tuː deɪz ˈnoʊtɪs/</div>
                    <div class="text-gray-700">两天的通知</div>
                </div>

                <div class="card bg-amber-100 border border-amber-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">三周时间</div>
                    <div class="keyword text-lg mb-1">three weeks' training</div>
                    <div class="text-sm text-gray-600 mb-1">/θriː wiːks ˈtreɪnɪŋ/</div>
                    <div class="text-gray-700">三周的培训</div>
                </div>

                <div class="card bg-stone-100 border border-stone-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">几小时时间</div>
                    <div class="keyword text-lg mb-1">five hours' sleep</div>
                    <div class="text-sm text-gray-600 mb-1">/faɪv ˈaʊərz sliːp/</div>
                    <div class="text-gray-700">五小时的睡眠</div>
                </div>

                <div class="card bg-neutral-100 border border-neutral-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">几分钟时间</div>
                    <div class="keyword text-lg mb-1">ten minutes' walk</div>
                    <div class="text-sm text-gray-600 mb-1">/ten ˈmɪnɪts wɔːk/</div>
                    <div class="text-gray-700">十分钟的步行</div>
                </div>

                <div class="card bg-zinc-100 border border-zinc-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">今天时间</div>
                    <div class="keyword text-lg mb-1">today's meeting</div>
                    <div class="text-sm text-gray-600 mb-1">/təˈdeɪz ˈmiːtɪŋ/</div>
                    <div class="text-gray-700">今天的会议</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">距离所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-slate-100 border border-slate-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一英里距离</div>
                    <div class="keyword text-lg mb-1">a mile's distance</div>
                    <div class="text-sm text-gray-600 mb-1">/ə maɪlz ˈdɪstəns/</div>
                    <div class="text-gray-700">一英里的距离</div>
                </div>

                <div class="card bg-gray-100 border border-gray-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">几公里距离</div>
                    <div class="keyword text-lg mb-1">five kilometers' journey</div>
                    <div class="text-sm text-gray-600 mb-1">/faɪv kɪˈlɒmɪtərz ˈdʒɜːrni/</div>
                    <div class="text-gray-700">五公里的旅程</div>
                </div>

                <div class="card bg-red-200 border border-red-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">石头投掷距离</div>
                    <div class="keyword text-lg mb-1">a stone's throw</div>
                    <div class="text-sm text-gray-600 mb-1">/ə stoʊnz θroʊ/</div>
                    <div class="text-gray-700">一石之遥（很近的距离）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">价值所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-200 border border-blue-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">金钱价值</div>
                    <div class="keyword text-lg mb-1">a dollar's worth</div>
                    <div class="text-sm text-gray-600 mb-1">/ə ˈdɑːlərz wɜːrθ/</div>
                    <div class="text-gray-700">一美元的价值</div>
                </div>

                <div class="card bg-green-200 border border-green-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">十美元价值</div>
                    <div class="keyword text-lg mb-1">ten dollars' worth</div>
                    <div class="text-sm text-gray-600 mb-1">/ten ˈdɑːlərz wɜːrθ/</div>
                    <div class="text-gray-700">十美元的价值</div>
                </div>

                <div class="card bg-yellow-200 border border-yellow-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">努力价值</div>
                    <div class="keyword text-lg mb-1">money's worth</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmʌniz wɜːrθ/</div>
                    <div class="text-gray-700">物有所值</div>
                </div>
            </div>
        </section>

        <!-- 常见错误和易混淆用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">常见错误和易混淆用法</h2>
            <p class="text-gray-700 mb-4">撇号的使用中有一些常见的错误和容易混淆的地方，掌握这些要点可以避免常见的语法错误。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">its vs it's</h3>
            <p class="text-gray-700 mb-4">这是最常见的撇号使用错误之一。its是物主代词，it's是it is或it has的缩写。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-200 border border-purple-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">物主代词</div>
                    <div class="keyword text-lg mb-1">its color</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ˈkʌlər/</div>
                    <div class="text-gray-700">它的颜色</div>
                </div>

                <div class="card bg-pink-200 border border-pink-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写形式</div>
                    <div class="keyword text-lg mb-1">it's raining</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ˈreɪnɪŋ/</div>
                    <div class="text-gray-700">正在下雨（it is raining）</div>
                </div>

                <div class="card bg-indigo-200 border border-indigo-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动物所有格</div>
                    <div class="keyword text-lg mb-1">The dog wagged its tail</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə dɔːɡ wæɡd ɪts teɪl/</div>
                    <div class="text-gray-700">狗摇了摇它的尾巴</div>
                </div>

                <div class="card bg-teal-200 border border-teal-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完成时缩写</div>
                    <div class="keyword text-lg mb-1">it's been a long day</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts biːn ə lɔːŋ deɪ/</div>
                    <div class="text-gray-700">这是漫长的一天（it has been）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">your vs you're</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-200 border border-orange-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">物主代词</div>
                    <div class="keyword text-lg mb-1">your book</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr bʊk/</div>
                    <div class="text-gray-700">你的书</div>
                </div>

                <div class="card bg-cyan-200 border border-cyan-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写形式</div>
                    <div class="keyword text-lg mb-1">you're welcome</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈwelkəm/</div>
                    <div class="text-gray-700">不客气（you are welcome）</div>
                </div>

                <div class="card bg-lime-200 border border-lime-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">所有物</div>
                    <div class="keyword text-lg mb-1">your family</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈfæməli/</div>
                    <div class="text-gray-700">你的家人</div>
                </div>

                <div class="card bg-emerald-200 border border-emerald-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态描述</div>
                    <div class="keyword text-lg mb-1">you're tired</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈtaɪərd/</div>
                    <div class="text-gray-700">你累了（you are tired）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">their vs they're vs there</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-violet-200 border border-violet-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">物主代词</div>
                    <div class="keyword text-lg mb-1">their house</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer haʊs/</div>
                    <div class="text-gray-700">他们的房子</div>
                </div>

                <div class="card bg-rose-200 border border-rose-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写形式</div>
                    <div class="keyword text-lg mb-1">they're coming</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">他们来了（they are coming）</div>
                </div>

                <div class="card bg-sky-200 border border-sky-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点副词</div>
                    <div class="keyword text-lg mb-1">over there</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊvər ðer/</div>
                    <div class="text-gray-700">在那边</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">who's vs whose</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-amber-200 border border-amber-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写形式</div>
                    <div class="keyword text-lg mb-1">who's there?</div>
                    <div class="text-sm text-gray-600 mb-1">/huːz ðer/</div>
                    <div class="text-gray-700">谁在那里？（who is there）</div>
                </div>

                <div class="card bg-stone-200 border border-stone-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问代词</div>
                    <div class="keyword text-lg mb-1">whose book is this?</div>
                    <div class="text-sm text-gray-600 mb-1">/huːz bʊk ɪz ðɪs/</div>
                    <div class="text-gray-700">这是谁的书？</div>
                </div>

                <div class="card bg-neutral-200 border border-neutral-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完成时缩写</div>
                    <div class="keyword text-lg mb-1">who's been here?</div>
                    <div class="text-sm text-gray-600 mb-1">/huːz biːn hɪr/</div>
                    <div class="text-gray-700">谁来过这里？（who has been）</div>
                </div>

                <div class="card bg-zinc-200 border border-zinc-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">所有格疑问</div>
                    <div class="keyword text-lg mb-1">whose turn is it?</div>
                    <div class="text-sm text-gray-600 mb-1">/huːz tɜːrn ɪz ɪt/</div>
                    <div class="text-gray-700">轮到谁了？</div>
                </div>
            </div>
        </section>

        <!-- 复数名词的撇号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">复数名词的撇号使用</h2>
            <p class="text-gray-700 mb-4">复数名词的撇号使用规则相对复杂，需要根据名词的复数形式来决定撇号的位置。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">规则复数名词</h3>
            <p class="text-gray-700 mb-4">对于以s结尾的规则复数名词，只需在末尾加撇号（'）。</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-slate-200 border border-slate-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学生复数</div>
                    <div class="keyword text-lg mb-1">the students' classroom</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstjuːdənts ˈklæsruːm/</div>
                    <div class="text-gray-700">学生们的教室</div>
                </div>

                <div class="card bg-gray-200 border border-gray-400 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">老师复数</div>
                    <div class="keyword text-lg mb-1">the teachers' lounge</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiːtʃərz laʊndʒ/</div>
                    <div class="text-gray-700">老师们的休息室</div>
                </div>

                <div class="card bg-red-300 border border-red-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">工人复数</div>
                    <div class="keyword text-lg mb-1">the workers' union</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈwɜːrkərz ˈjuːnjən/</div>
                    <div class="text-gray-700">工人们的工会</div>
                </div>

                <div class="card bg-blue-300 border border-blue-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">医生复数</div>
                    <div class="keyword text-lg mb-1">the doctors' office</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈdɑːktərz ˈɔːfɪs/</div>
                    <div class="text-gray-700">医生们的办公室</div>
                </div>

                <div class="card bg-green-300 border border-green-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">司机复数</div>
                    <div class="keyword text-lg mb-1">the drivers' licenses</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈdraɪvərz ˈlaɪsənsɪz/</div>
                    <div class="text-gray-700">司机们的驾照</div>
                </div>

                <div class="card bg-yellow-300 border border-yellow-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">父母复数</div>
                    <div class="keyword text-lg mb-1">the parents' meeting</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈperənts ˈmiːtɪŋ/</div>
                    <div class="text-gray-700">家长们的会议</div>
                </div>

                <div class="card bg-purple-300 border border-purple-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">朋友复数</div>
                    <div class="keyword text-lg mb-1">my friends' party</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ frends ˈpɑːrti/</div>
                    <div class="text-gray-700">我朋友们的聚会</div>
                </div>

                <div class="card bg-pink-300 border border-pink-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">邻居复数</div>
                    <div class="keyword text-lg mb-1">the neighbors' garden</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈneɪbərz ˈɡɑːrdən/</div>
                    <div class="text-gray-700">邻居们的花园</div>
                </div>

                <div class="card bg-indigo-300 border border-indigo-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客户复数</div>
                    <div class="keyword text-lg mb-1">the customers' feedback</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌstəmərz ˈfiːdbæk/</div>
                    <div class="text-gray-700">客户们的反馈</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">不规则复数名词</h3>
            <p class="text-gray-700 mb-4">对于不以s结尾的不规则复数名词，需要加撇号和s（'s）。</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-300 border border-teal-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">孩子复数</div>
                    <div class="keyword text-lg mb-1">the children's playground</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrənz ˈpleɪɡraʊnd/</div>
                    <div class="text-gray-700">孩子们的游乐场</div>
                </div>

                <div class="card bg-orange-300 border border-orange-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">男人复数</div>
                    <div class="keyword text-lg mb-1">the men's room</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə menz ruːm/</div>
                    <div class="text-gray-700">男士洗手间</div>
                </div>

                <div class="card bg-cyan-300 border border-cyan-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">女人复数</div>
                    <div class="keyword text-lg mb-1">the women's club</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈwɪmənz klʌb/</div>
                    <div class="text-gray-700">女士俱乐部</div>
                </div>

                <div class="card bg-lime-300 border border-lime-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">人们复数</div>
                    <div class="keyword text-lg mb-1">the people's choice</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈpiːpəlz tʃɔɪs/</div>
                    <div class="text-gray-700">人民的选择</div>
                </div>

                <div class="card bg-emerald-300 border border-emerald-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">老鼠复数</div>
                    <div class="keyword text-lg mb-1">the mice's nest</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə maɪs nest/</div>
                    <div class="text-gray-700">老鼠们的窝</div>
                </div>

                <div class="card bg-violet-300 border border-violet-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">鹅复数</div>
                    <div class="keyword text-lg mb-1">the geese's migration</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ɡiːs maɪˈɡreɪʃən/</div>
                    <div class="text-gray-700">鹅群的迁徙</div>
                </div>

                <div class="card bg-rose-300 border border-rose-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">脚复数</div>
                    <div class="keyword text-lg mb-1">the feet's position</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə fiːt pəˈzɪʃən/</div>
                    <div class="text-gray-700">脚的位置</div>
                </div>

                <div class="card bg-sky-300 border border-sky-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">牙齿复数</div>
                    <div class="keyword text-lg mb-1">the teeth's condition</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tiːθ kənˈdɪʃən/</div>
                    <div class="text-gray-700">牙齿的状况</div>
                </div>

                <div class="card bg-amber-300 border border-amber-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">牛复数</div>
                    <div class="keyword text-lg mb-1">the cattle's grazing</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkætəlz ˈɡreɪzɪŋ/</div>
                    <div class="text-gray-700">牛群的放牧</div>
                </div>
            </div>
        </section>

        <!-- 专有名词的撇号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">专有名词的撇号使用</h2>
            <p class="text-gray-700 mb-4">专有名词（人名、地名、公司名等）的撇号使用有其特殊规则，需要特别注意。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">人名所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-stone-300 border border-stone-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">普通人名</div>
                    <div class="keyword text-lg mb-1">Michael's car</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmaɪkəlz kɑːr/</div>
                    <div class="text-gray-700">迈克尔的汽车</div>
                </div>

                <div class="card bg-neutral-300 border border-neutral-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">女性人名</div>
                    <div class="keyword text-lg mb-1">Sarah's house</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈserəz haʊs/</div>
                    <div class="text-gray-700">莎拉的房子</div>
                </div>

                <div class="card bg-zinc-300 border border-zinc-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合人名</div>
                    <div class="keyword text-lg mb-1">Mary Jane's book</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmeri dʒeɪnz bʊk/</div>
                    <div class="text-gray-700">玛丽·简的书</div>
                </div>

                <div class="card bg-slate-300 border border-slate-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">姓氏所有格</div>
                    <div class="keyword text-lg mb-1">Mr. Johnson's office</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɪstər ˈdʒɒnsənz ˈɔːfɪs/</div>
                    <div class="text-gray-700">约翰逊先生的办公室</div>
                </div>

                <div class="card bg-gray-300 border border-gray-500 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">博士头衔</div>
                    <div class="keyword text-lg mb-1">Dr. Smith's clinic</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɑːktər smɪθs ˈklɪnɪk/</div>
                    <div class="text-gray-700">史密斯医生的诊所</div>
                </div>

                <div class="card bg-red-400 border border-red-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">教授头衔</div>
                    <div class="keyword text-lg mb-1">Professor Brown's lecture</div>
                    <div class="text-sm text-gray-600 mb-1">/prəˈfesər braʊnz ˈlektʃər/</div>
                    <div class="text-gray-700">布朗教授的讲座</div>
                </div>

                <div class="card bg-blue-400 border border-blue-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">总统头衔</div>
                    <div class="keyword text-lg mb-1">President Wilson's speech</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈprezɪdənt ˈwɪlsənz spiːtʃ/</div>
                    <div class="text-gray-700">威尔逊总统的演讲</div>
                </div>

                <div class="card bg-green-400 border border-green-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">队长头衔</div>
                    <div class="keyword text-lg mb-1">Captain Miller's orders</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkæptən ˈmɪlərz ˈɔːrdərz/</div>
                    <div class="text-gray-700">米勒队长的命令</div>
                </div>

                <div class="card bg-yellow-400 border border-yellow-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法官头衔</div>
                    <div class="keyword text-lg mb-1">Judge Davis's ruling</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒʌdʒ ˈdeɪvɪsɪz ˈruːlɪŋ/</div>
                    <div class="text-gray-700">戴维斯法官的裁决</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">地名所有格</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-400 border border-purple-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">城市名称</div>
                    <div class="keyword text-lg mb-1">New York's skyline</div>
                    <div class="text-sm text-gray-600 mb-1">/nuː jɔːrks ˈskaɪlaɪn/</div>
                    <div class="text-gray-700">纽约的天际线</div>
                </div>

                <div class="card bg-pink-400 border border-pink-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">州名所有格</div>
                    <div class="keyword text-lg mb-1">California's beaches</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌkæləˈfɔːrnjəz ˈbiːtʃɪz/</div>
                    <div class="text-gray-700">加利福尼亚的海滩</div>
                </div>

                <div class="card bg-indigo-400 border border-indigo-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">国家名称</div>
                    <div class="keyword text-lg mb-1">America's history</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈmerɪkəz ˈhɪstəri/</div>
                    <div class="text-gray-700">美国的历史</div>
                </div>

                <div class="card bg-teal-400 border border-teal-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">大陆名称</div>
                    <div class="keyword text-lg mb-1">Europe's culture</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈjʊrəps ˈkʌltʃər/</div>
                    <div class="text-gray-700">欧洲的文化</div>
                </div>

                <div class="card bg-orange-400 border border-orange-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">河流名称</div>
                    <div class="keyword text-lg mb-1">the Mississippi's length</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˌmɪsəˈsɪpiz leŋθ/</div>
                    <div class="text-gray-700">密西西比河的长度</div>
                </div>

                <div class="card bg-cyan-400 border border-cyan-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">山脉名称</div>
                    <div class="keyword text-lg mb-1">the Rockies' height</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈrɒkiz haɪt/</div>
                    <div class="text-gray-700">落基山脉的高度</div>
                </div>

                <div class="card bg-lime-400 border border-lime-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">湖泊名称</div>
                    <div class="keyword text-lg mb-1">Lake Michigan's shore</div>
                    <div class="text-sm text-gray-600 mb-1">/leɪk ˈmɪʃɪɡənz ʃɔːr/</div>
                    <div class="text-gray-700">密歇根湖的湖岸</div>
                </div>

                <div class="card bg-emerald-400 border border-emerald-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">岛屿名称</div>
                    <div class="keyword text-lg mb-1">Hawaii's climate</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈwaɪiz ˈklaɪmət/</div>
                    <div class="text-gray-700">夏威夷的气候</div>
                </div>

                <div class="card bg-violet-400 border border-violet-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">沙漠名称</div>
                    <div class="keyword text-lg mb-1">the Sahara's vastness</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə səˈhɑːrəz ˈvæstnəs/</div>
                    <div class="text-gray-700">撒哈拉沙漠的广阔</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">公司和组织名称</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-rose-400 border border-rose-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">科技公司</div>
                    <div class="keyword text-lg mb-1">Apple's products</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæpəlz ˈprɒdʌkts/</div>
                    <div class="text-gray-700">苹果公司的产品</div>
                </div>

                <div class="card bg-sky-400 border border-sky-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">汽车公司</div>
                    <div class="keyword text-lg mb-1">Ford's factory</div>
                    <div class="text-sm text-gray-600 mb-1">/fɔːrdz ˈfæktəri/</div>
                    <div class="text-gray-700">福特公司的工厂</div>
                </div>

                <div class="card bg-amber-400 border border-amber-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">银行机构</div>
                    <div class="keyword text-lg mb-1">the bank's policy</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bæŋks ˈpɒləsi/</div>
                    <div class="text-gray-700">银行的政策</div>
                </div>

                <div class="card bg-stone-400 border border-stone-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">大学机构</div>
                    <div class="keyword text-lg mb-1">Harvard's campus</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhɑːrvərdz ˈkæmpəs/</div>
                    <div class="text-gray-700">哈佛大学的校园</div>
                </div>

                <div class="card bg-neutral-400 border border-neutral-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">医院机构</div>
                    <div class="keyword text-lg mb-1">the hospital's staff</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈhɒspɪtəlz stæf/</div>
                    <div class="text-gray-700">医院的员工</div>
                </div>

                <div class="card bg-zinc-400 border border-zinc-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">政府部门</div>
                    <div class="keyword text-lg mb-1">the government's decision</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈɡʌvərnmənts dɪˈsɪʒən/</div>
                    <div class="text-gray-700">政府的决定</div>
                </div>

                <div class="card bg-slate-400 border border-slate-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">体育团队</div>
                    <div class="keyword text-lg mb-1">the team's victory</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tiːmz ˈvɪktəri/</div>
                    <div class="text-gray-700">团队的胜利</div>
                </div>

                <div class="card bg-gray-400 border border-gray-600 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">慈善组织</div>
                    <div class="keyword text-lg mb-1">the charity's mission</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃerəti mɪʃən/</div>
                    <div class="text-gray-700">慈善机构的使命</div>
                </div>

                <div class="card bg-red-500 border border-red-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">新闻媒体</div>
                    <div class="keyword text-lg mb-1">CNN's report</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌsiː en ˈenz rɪˈpɔːrt/</div>
                    <div class="text-gray-700">CNN的报道</div>
                </div>
            </div>
        </section>

        <!-- 撇号在引用和文学作品中的使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号在引用和文学作品中的使用</h2>
            <p class="text-gray-700 mb-4">在引用他人话语、文学作品标题和特殊表达中，撇号有其独特的使用方式。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">文学作品标题</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-500 border border-blue-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">小说标题</div>
                    <div class="keyword text-lg mb-1">Hemingway's "The Sun Also Rises"</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhemɪŋweɪz ðə sʌn ˈɔːlsoʊ ˈraɪzɪz/</div>
                    <div class="text-gray-700">海明威的《太阳照常升起》</div>
                </div>

                <div class="card bg-green-500 border border-green-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">诗歌标题</div>
                    <div class="keyword text-lg mb-1">Frost's "The Road Not Taken"</div>
                    <div class="text-sm text-gray-600 mb-1">/frɔːsts ðə roʊd nɑːt ˈteɪkən/</div>
                    <div class="text-gray-700">弗罗斯特的《未选择的路》</div>
                </div>

                <div class="card bg-yellow-500 border border-yellow-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">戏剧标题</div>
                    <div class="keyword text-lg mb-1">Shakespeare's "Hamlet"</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈʃeɪkspɪrz ˈhæmlət/</div>
                    <div class="text-gray-700">莎士比亚的《哈姆雷特》</div>
                </div>

                <div class="card bg-purple-500 border border-purple-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">短篇小说</div>
                    <div class="keyword text-lg mb-1">Poe's "The Raven"</div>
                    <div class="text-sm text-gray-600 mb-1">/poʊz ðə ˈreɪvən/</div>
                    <div class="text-gray-700">爱伦·坡的《乌鸦》</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引用中的撇号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-500 border border-pink-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引用</div>
                    <div class="keyword text-lg mb-1">"I can't believe it's true"</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliːv ɪts truː/</div>
                    <div class="text-gray-700">"我不敢相信这是真的"</div>
                </div>

                <div class="card bg-indigo-500 border border-indigo-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话引用</div>
                    <div class="keyword text-lg mb-1">"You're kidding!" she said</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkɪdɪŋ ʃi sed/</div>
                    <div class="text-gray-700">"你在开玩笑！"她说</div>
                </div>

                <div class="card bg-teal-500 border border-teal-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">名言引用</div>
                    <div class="keyword text-lg mb-1">"I have a dream"</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ə driːm/</div>
                    <div class="text-gray-700">"我有一个梦想"</div>
                </div>

                <div class="card bg-orange-500 border border-orange-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思想引用</div>
                    <div class="keyword text-lg mb-1">"What's happening?" he thought</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌts ˈhæpənɪŋ hi θɔːt/</div>
                    <div class="text-gray-700">"发生了什么？"他想</div>
                </div>
            </div>
        </section>

        <!-- 数字和字母的复数形式 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">数字和字母的复数形式</h2>
            <p class="text-gray-700 mb-4">在表示数字、字母、符号的复数形式时，传统上使用撇号，但现代英语中这种用法正在减少。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">数字复数</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-cyan-500 border border-cyan-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">年代表示</div>
                    <div class="keyword text-lg mb-1">the 1990's</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈnaɪntiːn ˈnaɪntiz/</div>
                    <div class="text-gray-700">20世纪90年代</div>
                </div>

                <div class="card bg-lime-500 border border-lime-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现代写法</div>
                    <div class="keyword text-lg mb-1">the 1990s</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈnaɪntiːn ˈnaɪntiz/</div>
                    <div class="text-gray-700">20世纪90年代（推荐）</div>
                </div>

                <div class="card bg-emerald-500 border border-emerald-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">温度复数</div>
                    <div class="keyword text-lg mb-1">in the 80s</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪn ðə ˈeɪtiz/</div>
                    <div class="text-gray-700">在80多度</div>
                </div>

                <div class="card bg-violet-500 border border-violet-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">分数复数</div>
                    <div class="keyword text-lg mb-1">two 1/2's</div>
                    <div class="text-sm text-gray-600 mb-1">/tuː hævz/</div>
                    <div class="text-gray-700">两个二分之一</div>
                </div>

                <div class="card bg-rose-500 border border-rose-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">单个数字</div>
                    <div class="keyword text-lg mb-1">three 7's</div>
                    <div class="text-sm text-gray-600 mb-1">/θriː ˈsevənz/</div>
                    <div class="text-gray-700">三个7</div>
                </div>

                <div class="card bg-sky-500 border border-sky-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现代数字</div>
                    <div class="keyword text-lg mb-1">three 7s</div>
                    <div class="text-sm text-gray-600 mb-1">/θriː ˈsevənz/</div>
                    <div class="text-gray-700">三个7（推荐）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">字母复数</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-amber-500 border border-amber-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">传统写法</div>
                    <div class="keyword text-lg mb-1">dot your i's</div>
                    <div class="text-sm text-gray-600 mb-1">/dɑːt jʊr aɪz/</div>
                    <div class="text-gray-700">给i加点</div>
                </div>

                <div class="card bg-stone-500 border border-stone-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现代写法</div>
                    <div class="keyword text-lg mb-1">dot your is</div>
                    <div class="text-sm text-gray-600 mb-1">/dɑːt jʊr aɪz/</div>
                    <div class="text-gray-700">给i加点（推荐）</div>
                </div>

                <div class="card bg-neutral-500 border border-neutral-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">字母等级</div>
                    <div class="keyword text-lg mb-1">all A's</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːl eɪz/</div>
                    <div class="text-gray-700">全A</div>
                </div>

                <div class="card bg-zinc-500 border border-zinc-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现代等级</div>
                    <div class="keyword text-lg mb-1">all As</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːl eɪz/</div>
                    <div class="text-gray-700">全A（推荐）</div>
                </div>

                <div class="card bg-slate-500 border border-slate-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">小写字母</div>
                    <div class="keyword text-lg mb-1">mind your p's and q's</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪnd jʊr piːz ænd kjuːz/</div>
                    <div class="text-gray-700">注意言行举止</div>
                </div>

                <div class="card bg-gray-500 border border-gray-700 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写复数</div>
                    <div class="keyword text-lg mb-1">several PhD's</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsevərəl ˌpiː eɪtʃ ˈdiːz/</div>
                    <div class="text-gray-700">几个博士学位</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">符号复数</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-600 border border-red-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">加号复数</div>
                    <div class="keyword text-lg mb-1">two +'s</div>
                    <div class="text-sm text-gray-600 mb-1">/tuː ˈplʌsɪz/</div>
                    <div class="text-gray-700">两个加号</div>
                </div>

                <div class="card bg-blue-600 border border-blue-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">减号复数</div>
                    <div class="keyword text-lg mb-1">several -'s</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsevərəl ˈmaɪnəsɪz/</div>
                    <div class="text-gray-700">几个减号</div>
                </div>

                <div class="card bg-green-600 border border-green-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">星号复数</div>
                    <div class="keyword text-lg mb-1">three *'s</div>
                    <div class="text-sm text-gray-600 mb-1">/θriː ˈæstərɪsks/</div>
                    <div class="text-gray-700">三个星号</div>
                </div>

                <div class="card bg-yellow-600 border border-yellow-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">问号复数</div>
                    <div class="keyword text-lg mb-1">multiple ?'s</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmʌltəpəl ˈkwestʃən mɑːrks/</div>
                    <div class="text-gray-700">多个问号</div>
                </div>

                <div class="card bg-purple-600 border border-purple-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹号复数</div>
                    <div class="keyword text-lg mb-1">several !'s</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsevərəl ˌekskləˈmeɪʃən mɑːrks/</div>
                    <div class="text-gray-700">几个感叹号</div>
                </div>

                <div class="card bg-pink-600 border border-pink-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">美元符号</div>
                    <div class="keyword text-lg mb-1">two $'s</div>
                    <div class="text-sm text-gray-600 mb-1">/tuː ˈdɑːlər saɪnz/</div>
                    <div class="text-gray-700">两个美元符号</div>
                </div>
            </div>
        </section>

        <!-- 撇号的打字和印刷规范 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号的打字和印刷规范</h2>
            <p class="text-gray-700 mb-4">在不同的打字和印刷环境中，撇号的形式和使用有一些技术性的考虑。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">撇号的形式</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-600 border border-indigo-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标准撇号</div>
                    <div class="keyword text-lg mb-1">'</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈpɒstrəfi/</div>
                    <div class="text-gray-700">弯曲的撇号（推荐）</div>
                </div>

                <div class="card bg-teal-600 border border-teal-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直撇号</div>
                    <div class="keyword text-lg mb-1">'</div>
                    <div class="text-sm text-gray-600 mb-1">/streɪt əˈpɒstrəfi/</div>
                    <div class="text-gray-700">直的撇号（打字机风格）</div>
                </div>

                <div class="card bg-orange-600 border border-orange-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确方向</div>
                    <div class="keyword text-lg mb-1">don't</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt/</div>
                    <div class="text-gray-700">撇号向右弯曲</div>
                </div>

                <div class="card bg-cyan-600 border border-cyan-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误方向</div>
                    <div class="keyword text-lg mb-1">don`t</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt/</div>
                    <div class="text-gray-700">反引号（错误）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">键盘输入</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-lime-600 border border-lime-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标准键盘</div>
                    <div class="keyword text-lg mb-1">Shift + '</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃɪft plʌs əˈpɒstrəfi/</div>
                    <div class="text-gray-700">按Shift加撇号键</div>
                </div>

                <div class="card bg-emerald-600 border border-emerald-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">智能输入</div>
                    <div class="keyword text-lg mb-1">Auto-correct</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɔːtoʊ kəˈrekt/</div>
                    <div class="text-gray-700">自动纠正为弯曲撇号</div>
                </div>

                <div class="card bg-violet-600 border border-violet-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">特殊字符</div>
                    <div class="keyword text-lg mb-1">Alt + 0146</div>
                    <div class="text-sm text-gray-600 mb-1">/ælt plʌs zɪroʊ wʌn fɔːr sɪks/</div>
                    <div class="text-gray-700">Windows特殊字符输入</div>
                </div>

                <div class="card bg-rose-600 border border-rose-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Mac输入</div>
                    <div class="keyword text-lg mb-1">Option + Shift + ]</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɒpʃən plʌs ʃɪft brækət/</div>
                    <div class="text-gray-700">Mac系统撇号输入</div>
                </div>
            </div>
        </section>

        <!-- 撇号使用的地域差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号使用的风格差异</h2>
            <p class="text-gray-700 mb-4">不同的写作风格指南对撇号的使用有细微的差异，了解这些差异有助于在不同场合正确使用。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">学术写作风格</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-600 border border-sky-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正式文体</div>
                    <div class="keyword text-lg mb-1">cannot</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkænɑːt/</div>
                    <div class="text-gray-700">避免缩写，使用完整形式</div>
                </div>

                <div class="card bg-amber-600 border border-amber-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">非正式文体</div>
                    <div class="keyword text-lg mb-1">can't</div>
                    <div class="text-sm text-gray-600 mb-1">/kænt/</div>
                    <div class="text-gray-700">可以使用缩写形式</div>
                </div>

                <div class="card bg-stone-600 border border-stone-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">论文写作</div>
                    <div class="keyword text-lg mb-1">it is important</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt ɪz ɪmˈpɔːrtənt/</div>
                    <div class="text-gray-700">学术论文避免it's</div>
                </div>

                <div class="card bg-neutral-600 border border-neutral-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常写作</div>
                    <div class="keyword text-lg mb-1">it's important</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɪmˈpɔːrtənt/</div>
                    <div class="text-gray-700">日常写作可用缩写</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">商务写作风格</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-zinc-600 border border-zinc-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正式商务</div>
                    <div class="keyword text-lg mb-1">We are pleased to inform</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ɑːr pliːzd tuː ɪnˈfɔːrm/</div>
                    <div class="text-gray-700">正式商务信函避免缩写</div>
                </div>

                <div class="card bg-slate-600 border border-slate-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">非正式商务</div>
                    <div class="keyword text-lg mb-1">We're pleased to inform</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪr pliːzd tuː ɪnˈfɔːrm/</div>
                    <div class="text-gray-700">内部邮件可用缩写</div>
                </div>

                <div class="card bg-gray-600 border border-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客户沟通</div>
                    <div class="keyword text-lg mb-1">Thank you for your inquiry</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk juː fɔːr jʊr ɪnˈkwaɪəri/</div>
                    <div class="text-gray-700">客户邮件保持正式</div>
                </div>

                <div class="card bg-red-700 border border-red-900 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">团队沟通</div>
                    <div class="keyword text-lg mb-1">Thanks for your help</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋks fɔːr jʊr help/</div>
                    <div class="text-gray-700">团队内部可以简化</div>
                </div>
            </div>
        </section>

        <!-- 撇号使用的现代趋势 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号使用的现代趋势</h2>
            <p class="text-gray-700 mb-4">随着语言的发展和数字化时代的到来，撇号的使用也在发生一些变化。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-700 border border-blue-900 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简化趋势</div>
                    <div class="keyword text-lg mb-1">1990s (not 1990's)</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnaɪntiːn ˈnaɪntiz/</div>
                    <div class="text-gray-700">年代表示趋向简化</div>
                </div>

                <div class="card bg-green-700 border border-green-900 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字媒体</div>
                    <div class="keyword text-lg mb-1">Social media shortcuts</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsoʊʃəl ˈmiːdiə ˈʃɔːrtkʌts/</div>
                    <div class="text-gray-700">社交媒体简化用法</div>
                </div>

                <div class="card bg-yellow-700 border border-yellow-900 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">技术影响</div>
                    <div class="keyword text-lg mb-1">Auto-correct features</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɔːtoʊ kəˈrekt ˈfiːtʃərz/</div>
                    <div class="text-gray-700">自动纠错功能影响</div>
                </div>

                <div class="card bg-purple-700 border border-purple-900 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">国际化</div>
                    <div class="keyword text-lg mb-1">Global communication</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɡloʊbəl kəˌmjuːnəˈkeɪʃən/</div>
                    <div class="text-gray-700">国际交流标准化</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
